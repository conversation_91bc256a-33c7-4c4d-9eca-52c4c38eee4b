#!/usr/bin/env python3
"""
构建DetectTrie并进行异常检测
"""

import sys
import os
import json
import pickle
from datetime import datetime

# 添加路径
sys.path.append('/data')
sys.path.append('/data/CloudTrie-Code')

from bgp_anomaly_detection_system import BGPAnomalyDetectionSystem
from DetectTrie import DetectTrie

def build_detect_trie_and_detect(event_name, uncertainty_threshold=0.5):
    """构建DetectTrie并进行异常检测"""
    
    print(f"🔍 构建DetectTrie并检测事件: {event_name}")
    print(f"📊 不确定度阈值: {uncertainty_threshold}")
    print("=" * 80)
    
    # 路径设置
    event_dir = f"/data/bgp_analysis/{event_name}"
    uncertainty_file = f"{event_dir}/prefix_uncertainties.json"
    ip_trie_file = f"{event_dir}/ip_trie.dat"
    detect_trie_file = f"{event_dir}/detect_trie.dat"
    
    # 检查文件是否存在
    if not os.path.exists(uncertainty_file):
        print(f"❌ 不确定度文件不存在: {uncertainty_file}")
        return
    
    if not os.path.exists(ip_trie_file):
        print(f"❌ IPTrie文件不存在: {ip_trie_file}")
        return
    
    # 1. 加载不确定度数据
    print("📂 加载不确定度数据...")
    with open(uncertainty_file, 'r') as f:
        uncertainty_data = json.load(f)
    
    uncertainties = uncertainty_data['uncertainties']
    print(f"   ✅ 加载了 {len(uncertainties):,} 个前缀的不确定度")
    
    # 统计不确定度分布
    total_po_pairs = 0
    low_uncertainty_pairs = 0
    high_uncertainty_pairs = 0
    
    for prefix, results in uncertainties.items():
        for result in results:
            total_po_pairs += 1
            uncertainty = result["uncertainty"]
            if uncertainty <= uncertainty_threshold:
                low_uncertainty_pairs += 1
            else:
                high_uncertainty_pairs += 1
    
    print(f"   📊 总P/O对数: {total_po_pairs:,}")
    print(f"   📊 低不确定度对数: {low_uncertainty_pairs:,} ({low_uncertainty_pairs/total_po_pairs*100:.1f}%)")
    print(f"   📊 高不确定度对数: {high_uncertainty_pairs:,} ({high_uncertainty_pairs/total_po_pairs*100:.1f}%)")
    
    # 2. 加载IPTrie
    print("\n📂 加载IPTrie...")
    with open(ip_trie_file, 'rb') as f:
        ip_trie = pickle.load(f)
    print("   ✅ IPTrie加载完成")
    
    # 3. 构建DetectTrie
    print("\n🔧 构建DetectTrie...")
    detect_trie = DetectTrie()
    
    # 遍历所有前缀，根据不确定度阈值构建DetectTrie
    added_prefixes = 0
    skipped_prefixes = 0
    
    for prefix, results in uncertainties.items():
        # 检查该前缀是否有低不确定度的P/O对
        has_low_uncertainty = False
        for result in results:
            if result["uncertainty"] <= uncertainty_threshold:
                has_low_uncertainty = True
                break
        
        if has_low_uncertainty:
            # 添加到DetectTrie（只添加低不确定度的P/O对）
            for result in results:
                if result["uncertainty"] <= uncertainty_threshold:
                    asn = result["asn"]
                    uncertainty = result["uncertainty"]
                    
                    # 添加到DetectTrie
                    detect_trie.insert(prefix, asn, uncertainty)
            
            added_prefixes += 1
        else:
            skipped_prefixes += 1
    
    print(f"   ✅ DetectTrie构建完成")
    print(f"   📊 添加的前缀数: {added_prefixes:,}")
    print(f"   📊 跳过的前缀数: {skipped_prefixes:,}")
    
    # 4. 保存DetectTrie
    print(f"\n💾 保存DetectTrie到: {detect_trie_file}")
    with open(detect_trie_file, 'wb') as f:
        pickle.dump(detect_trie, f)
    print("   ✅ DetectTrie保存完成")
    
    # 5. 进行异常检测
    print("\n🔍 开始异常检测...")
    
    # 初始化BGP异常检测系统
    system = BGPAnomalyDetectionSystem()
    
    # 加载BGP数据进行检测
    bgp_data_dir = f"{event_dir}/bgp_data"
    if not os.path.exists(bgp_data_dir):
        print(f"❌ BGP数据目录不存在: {bgp_data_dir}")
        return
    
    # 检测异常
    detection_results = []
    
    # 遍历BGP数据文件
    bgp_files = []
    for root, dirs, files in os.walk(bgp_data_dir):
        for file in files:
            if file.endswith('.gz') or file.endswith('.txt'):
                bgp_files.append(os.path.join(root, file))
    
    print(f"   📂 找到 {len(bgp_files)} 个BGP数据文件")
    
    # 简化检测：检查前10个文件
    for i, bgp_file in enumerate(bgp_files[:10]):
        print(f"   🔍 检测文件 {i+1}/10: {os.path.basename(bgp_file)}")
        
        try:
            # 解析BGP数据
            bgp_entries = system.parse_bgp_file(bgp_file)
            
            # 对每个BGP条目进行检测
            file_anomalies = 0
            for entry in bgp_entries[:100]:  # 限制每个文件检测100条
                prefix = entry.get('prefix', '')
                origin_as = entry.get('origin_as', '')
                
                if prefix and origin_as:
                    # 转换前缀为二进制
                    try:
                        binary_prefix = system.ip_to_binary_prefix(prefix)
                        
                        # 在DetectTrie中查找
                        is_anomaly = detect_trie.detect_anomaly(binary_prefix, origin_as)
                        
                        if is_anomaly:
                            file_anomalies += 1
                            detection_results.append({
                                'file': os.path.basename(bgp_file),
                                'prefix': prefix,
                                'origin_as': origin_as,
                                'timestamp': entry.get('timestamp', ''),
                                'type': 'potential_hijack'
                            })
                    except Exception as e:
                        continue
            
            print(f"      🚨 发现异常: {file_anomalies} 个")
            
        except Exception as e:
            print(f"      ❌ 文件处理失败: {e}")
            continue
    
    # 6. 保存检测结果
    results_file = f"{event_dir}/detection_results.json"
    print(f"\n💾 保存检测结果到: {results_file}")
    
    final_results = {
        'event_name': event_name,
        'uncertainty_threshold': uncertainty_threshold,
        'detection_timestamp': datetime.now().isoformat(),
        'statistics': {
            'total_po_pairs': total_po_pairs,
            'low_uncertainty_pairs': low_uncertainty_pairs,
            'high_uncertainty_pairs': high_uncertainty_pairs,
            'added_prefixes': added_prefixes,
            'skipped_prefixes': skipped_prefixes,
            'total_anomalies': len(detection_results)
        },
        'anomalies': detection_results
    }
    
    with open(results_file, 'w') as f:
        json.dump(final_results, f, indent=2)
    
    print("   ✅ 检测结果保存完成")
    
    # 7. 显示检测摘要
    print(f"\n📊 检测摘要:")
    print(f"   🎯 使用阈值: {uncertainty_threshold}")
    print(f"   📈 低不确定度P/O对: {low_uncertainty_pairs:,} ({low_uncertainty_pairs/total_po_pairs*100:.1f}%)")
    print(f"   🔧 DetectTrie前缀数: {added_prefixes:,}")
    print(f"   🚨 检测到异常: {len(detection_results)} 个")
    
    if detection_results:
        print(f"\n🔍 异常样本:")
        for i, anomaly in enumerate(detection_results[:5]):
            print(f"   {i+1}. {anomaly['prefix']} -> AS{anomaly['origin_as']} ({anomaly['file']})")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='构建DetectTrie并进行异常检测')
    parser.add_argument('--event-name', required=True, help='事件名称')
    parser.add_argument('--threshold', type=float, default=0.5, help='不确定度阈值')
    
    args = parser.parse_args()
    
    build_detect_trie_and_detect(args.event_name, args.threshold)
